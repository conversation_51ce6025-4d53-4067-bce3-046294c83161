import json
from contextlib import contextmanager
from datetime import datetime
import psycopg2
from psycopg2.extras import <PERSON><PERSON>
from typing import List, Optional
from config.config import MODELS_CONFIG
from core.config import settings
from models.enums import StatusInterview
from models.llm import inference_with_fallback, get_related_class_definitions
from models.interview import (
    InterviewCreate,
    QA_model
)
from langchain_core.messages import HumanMessage
from models.interview import (
    InterviewProcessingRequest,
    ExtractedAnswers,
    ParaphrasedAnswers,
    ProcessType,
    QA_model,
    EvaluationResult,
    EvaluateInterviewNoQA
)
from models.models import Position, QuestionsBase, SingleQuestions
from models.interview import Interview, InterviewHr, InterviewTec
from fastapi import HTTPException
from controllers.positions_controller import get_position_by_id
from controllers.candidates_controller import get_candidate_by_id


# DB helper to get cursor
@contextmanager
def get_cursor():
    conn = psycopg2.connect(settings.DATABASE_URL)
    try:
        with conn:
            with conn.cursor() as cur:
                yield cur
    finally:
        conn.close()


# Helper function to build text for LLM prompts
# This function constructs a text prompt based on the provided items.
def get_topics(include: str) -> str:
    base = ""
    desired_order = ['Technical Skills', 'Methodologies', 'Soft Skills', 'Language - Tools']

    # Map lowercase to original case
    lower_to_original = {item.lower(): item for item in desired_order}

    # Normalize input items to lowercase
    input_items = [item.strip().lower() for item in include.split(",") if item.strip()]

    if not input_items:
        return f"{base}{', '.join(desired_order)}."

    # Keep the order from desired_order and match only those present
    ordered = [lower_to_original[item.lower()] for item in desired_order if item.lower() in input_items]

    return f"{base}{', '.join(ordered)}."


# Core business logic for processing interviews
def process_interview(request: InterviewProcessingRequest):
    """Business logic formerly inside router."""
    if request.process_type == ProcessType.EXTRACT:
        schema = ExtractedAnswers
        task_prompt = (
            "Extract direct answers from the transcript for each question. "
            "Return ONLY the candidate's responses matching each question in order."
        )
    else:
        schema = ParaphrasedAnswers
        task_prompt = (
            "Paraphrase the candidate's answers using the full context of the transcript, ensuring that:\n"
            "- The paraphrased answer remains faithful to what was actually said.\n"
            "- If relevant details appear in other parts of the transcript, include a 'complement_from' field.\n"
            "- Do NOT introduce new information or modify qualifications.\n"
            "- Return JSON following the provided schema."
        )

    user_msg = HumanMessage(
        content="Questions:\n"
        + "\n".join(f"{i + 1}. {q}" for i, q in enumerate(request.questions))
        + "\n\nTranscript:\n"
        + request.transcript
    )

    schema_text = get_related_class_definitions(schema)
    result = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=schema,
        user_messages=[user_msg],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    if not result:
        raise RuntimeError("All LLM providers failed")
    return result  


# Helper function to run and persist interview processing
def run_and_persist_interview(interview_id: str, process_type: ProcessType):
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT iq.data, i.transcript_tec
            FROM interview_questions iq
            JOIN interviews i ON iq.position_id = i.position_id
            WHERE i.id = %s;
            """,
            (interview_id,)
        )
        row = cur.fetchone()
    if not row:
        # raise HTTPException(status_code=404, detail="Interview or questionnaire not found")
        return None

    questionnaire, transcript = row
    questions = [q["question"] for q in questionnaire["questions"]]

    req = InterviewProcessingRequest(
        questions=questions, transcript=transcript, process_type=process_type
    )
    result = process_interview(req)

    with get_cursor() as cur:
        cur.execute(
            """
            UPDATE interviews
            SET anwers_data = %s,
                updated_at = NOW()
            WHERE id = %s;
            """,
            (Json(result.model_dump()), interview_id)
        )
    return result


def evaluate_interview_with_no_qa(interview_id: str) -> EvaluationResult:
    # We should compare transcript, candidate info and position info
    # interviews has position_id, candidate_id, id, transcript_hr
    # For position info we need to retrieve it from positions_smarthr
    # For candidate info we need to retrieve it from candidates_smarthr
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT p.position_info, c.candidate_info, i.transcript_hr
                FROM interviews i
                JOIN positions_smarthr p ON i.position_id = p.id
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.id = %s;
                """,
                (interview_id,)
            )
            row = cur.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail="Interview not found (evaluate_interview_with_no_qa)")
        position_info, candidate_info, transcript_hr = row
        print(f"Position info: {position_info}, Candidate info: {candidate_info}, Transcript: {transcript_hr}")
        # """ SCHEMA
        # class EvaluateInterviewNoQA(BaseModel):
        #     overall_seniority: Seniority
        #     percentage_of_match: float
        #     explanation: str
        # """
        task_prompt_no_questions = """
            Evaluate the interview transcript, candidate info and position info.
            Return JSON that matches the provided schema.
            IMPORTANT RULES:
            • Provide an overall_seniority (senior|mid|junior) based on the transcript and candidate info.
        """
        schema_text = get_related_class_definitions(EvaluateInterviewNoQA)
        result = inference_with_fallback(
            task_prompt=task_prompt_no_questions,
            model_schema=EvaluateInterviewNoQA,
            user_messages=[HumanMessage(content=json.dumps({'position_info': position_info, 'candidate_info': candidate_info, 'transcript': transcript_hr}, ensure_ascii=False))],
            model_schema_text=schema_text,
            models_order=MODELS_CONFIG["default_models_order"],
        )
        print(f"Result from LLM: {result}")
        if not result:
            raise RuntimeError("LLM evaluation failed")

        with get_cursor() as cur:
            cur.execute(
                """
                UPDATE interviews
                SET interview_data = %s,
                    updated_at = NOW()
                WHERE id = %s;
                """,
                (Json(result.model_dump()), interview_id)
            )
        return result

    except Exception as e:
        # Log the error and raise an HTTPException
        print(f"Error occurred while evaluating interview without QA: {str(e)}")
        raise HTTPException(status_code=404, detail=f"Interview not found (except: evaluate_interview_with_no_qa): {str(e)}")


# Evaluate the interview by comparing candidate answers with expected answers
def evaluate_interview(interview_id: str) -> EvaluationResult:
    # Validate if interview_questions are not filled but transcript_hr is filled if yes evaluate_interview_with_no_qa
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT iq.data, i.anwers_data
            FROM interview_questions iq
            JOIN interviews i ON iq.position_id = i.position_id
            WHERE i.id = %s;
            """,
            (interview_id,)
        )
        row = cur.fetchone()
    if not row:
        return evaluate_interview_with_no_qa(interview_id)

    expected, actual = row
    # First validation: check if actual is not empty and expected has questions
    # if not actual:
    #     raise HTTPException(status_code=400, detail="Interview transcript is empty")
    if expected is None or not expected.get('questions'):
        return evaluate_interview_with_no_qa(interview_id)
    # elif expected is not None and expected.get('questions'):
    #     run_and_persist_interview(interview_id, ProcessType.EXTRACT)
    ## End first validation

    # Get additional context for better evaluation
    interview_context = fetch_interview_by_interview_id(interview_id)
    feedback_comments = interview_context.feedback_tec if interview_context else None
    transcript = interview_context.transcript_tec if interview_context else None

    task_prompt = (
    "Evaluate the candidate's responses against expected senior/mid/junior levels by integrating all available information. "
    "For each question, identify 'detected_seniority' (senior|mid|junior) and provide an explanation. "
    "Additionally, determine the 'overall_seniority' and calculate the 'percentage_of_match'. "
    "Deliver a JSON output conforming to the specified schema."
    "CRITICAL INTEGRATION REQUIREMENTS:\n"
    "• PRIORITIZE expert manual feedback comments where available, as they offer critical human insights.\n"
    "• Utilize the transcript to understand the conversation flow and the candidate's reasoning.\n"
    "• Cross-reference responses with feedback comments to uncover patterns and insights.\n"
    "• Link specific strengths/weaknesses noted in feedback comments to supporting evidence in the Q&A.\n"
    "• Acknowledge and explain discrepancies between comments and extracted answers.\n"
    "• Conclude with an 'overall_seniority' based on a holistic analysis of all data sources.\n"
    "• Include a detailed explanation of how comments, transcript, and answers were integrated.\n"
    "• Ensure the output is a valid JSON object with the correct structure and syntax.\n"
)
    schema_text = get_related_class_definitions(EvaluationResult)

    # Prepare comprehensive context for LLM
    evaluation_context = {
        'expected': expected,
        'actual': actual
    }

    # Add feedback comments if available
    if feedback_comments:
        evaluation_context['feedback_comments'] = feedback_comments

    # Add transcript excerpt if available (truncate if too long)
    if transcript:
        transcript_excerpt = transcript[:1000] + "..." if len(transcript) > 1000 else transcript
        evaluation_context['transcript_excerpt'] = transcript_excerpt

    result = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=EvaluationResult,
        user_messages=[HumanMessage(content=json.dumps(evaluation_context, ensure_ascii=False))],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    if not result:
        raise RuntimeError("LLM evaluation failed")

    with get_cursor() as cur:
        cur.execute(
            """
            UPDATE interviews
            SET interview_data = %s,
                updated_at = NOW()
            WHERE id = %s;
            """,
            (Json(result.model_dump()), interview_id)
        )
    return result


# Generate and persist interview questions
# This function generates interview questions based on the position ID and persists them in the database.
def generate_and_persist_qa(position_id: str, n_questions: int, include: str, current_user: str) -> QA_model:
    """
    1. Pull the full JSON description from positions_smarthr.position_info.
    2. Craft a richer prompt that includes BOTH the role title and the description.
    3. Call the LLM (structured output) to get 20 Q&A.
    4. Persist the result in interview_questions.
    """
    # 1) Fetch position
    position = get_position_by_id(position_id)
    if not position:
        raise HTTPException(status_code=404, detail="Position not found")

    # Check if questions already exist for this position
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT data, allow_regeneration FROM interview_questions WHERE position_id = %s;
            """,
            (position_id,)
        )
        existing_questions = cur.fetchone()
        # If questions exist and regeneration is not allowed, raise an error
        if existing_questions and not existing_questions.get('allow_regeneration', True):
            raise HTTPException(status_code=400, detail="Interview questions already exist for this position")

    # Adjust the field names if your JSON differs.
    info: dict = position.position_info or {}
    full_description = json.dumps(info, ensure_ascii=False)

    # 2) Build prompt
    topics_text = get_topics(include)
    task_prompt = f"""
        You are tasked with creating an interview questionnaire for a specific role. The focus is to craft questions that effectively evaluate technical skills and differentiate levels of seniority among candidates.

        Role Description:
        {full_description}

        **Please generate exactly {n_questions} questions pertaining to the following topics: {topics_text}. For each question, ensure you include**:

        **A question_number between 1 and {n_questions}**

        A tag indicating the specific topic addressed, chosen from: {topics_text}

        Three detailed answer levels to reflect specific experience dimensions:

        - senior_answer: Illustrate a broad and detailed technical scope. Discuss architecture decisions, performance optimization, scalability, system integrations, and trade-offs, while aligning with business needs. Incorporate quantifiable metrics where possible.

        - mid_answer: Offer clear and concise technical explanations. Discuss implementation steps, relevant tools, and best practices, showcasing initiative, cross-team collaboration, and an execution-oriented problem-solving focus.

        - junior_answer: Highlight basic technical knowledge, projects, and application of fundamentals. Focus on learning, feedback openness, and team contribution enthusiasm with simple, concrete explanations.

        Output the result in a valid JSON format with the necessary structure and syntax.

        IMPORTANT RULES:

        Each topic must have at least one question.
        Only include one tag per question, strictly from the allowed topics list: {topics_text}.
        Prohibit the 'SOFT SKILLS METHODOLOGIES' tag and similar combinations.
        **Ensure clear, verbose answers that increase in depth with seniority, without examples**.
        Distinctly differentiate answers by level; avoid repeating generalized content.
        Avoid phrases like 'As a junior/mid/senior...' or 'in my 2/5/8 years...', instead emphasize skills and traits relevant to each level.
        Answer Depth Example (Agile Methodologies — Sprint Planning):
        Junior: Focus on learning and basic Agile/Scrum understanding. Key skills: active listening, task breakdown. Traits: learner mindset, openness to feedback.
        Mid-Level: Engage in planning and collaboration with stakeholders. Key skills: backlog refinement, adaptability. Traits: initiative, autonomy.
        Senior: Lead sessions, aligning stakeholders and managing dependencies. Key skills: stakeholder alignment, facilitation. Traits: leadership, strategic thinking."
        """
    schema_text = get_related_class_definitions(QA_model)

    qa = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=QA_model,
        user_messages=[HumanMessage(content="")],  # no extra user message needed
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    if not qa:
        raise RuntimeError("LLM failed to generate questionnaire")

    # 3) Persist
    with get_cursor() as cur:
        cur.execute(
            """
            INSERT INTO interview_questions 
                (position_id, data, created_by, created_at, updated_by, updated_at) 
            VALUES 
                (%s, %s, %s, NOW(), %s, NOW())
            ON CONFLICT (position_id) DO UPDATE
            SET 
                data = EXCLUDED.data,
                updated_by = EXCLUDED.updated_by,
                updated_at = NOW();
            """,
            (position_id, Json(qa.model_dump()), current_user, current_user)
        )
    return qa


# Create interviews for the given position and candidates
# This function creates interviews for the given position and candidates.
def create_interview(position_id, analysis_data: list[InterviewCreate]) -> List[Interview]:
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()
        candidates_id = [data.candidate_id for data in analysis_data]
        print(f"Creating interview for position_id: {position_id} and candidates_id: {candidates_id}")
        for data in analysis_data:
            candidate_id = data.candidate_id
            print(f"Creating interview for candidate {candidate_id}, position {position_id}")
            if not candidate_id:
                print(f"Invalid candidate_id for candidate {candidate_id}, position {position_id}")
                continue
            # Validate if candidate_id is a valid UUID
            if not isinstance(candidate_id, str) or len(candidate_id) != 36:
                print(f"Invalid candidate_id format for candidate {candidate_id}, position {position_id}")
                continue
            # validate if candidate_id exists in candidates_smarthr table
            exist = get_candidate_by_id(candidate_id)
            if not exist:
                print(f"Candidate {candidate_id} does not exist in candidates_smarthr table")
                continue
            # Check if interview already exists for this candidate and position
            exist = fetch_interview_by_position_id_candidate_id(position_id, candidate_id)
            if exist:
                print(f"Interview already exists for candidate {candidate_id}, position {position_id}")
                continue
            # Insert new interview
            cur.execute(
                """
                INSERT INTO interviews 
                (position_id, candidate_id, analysis_data, status_hr, status_tec, created_at, updated_at)
                VALUES (%s, %s, %s, 'not_scheduled', 'not_scheduled', NOW(), NOW())
                RETURNING id, position_id, candidate_id
                """,
                (
                    position_id,
                    candidate_id,
                    Json(data.analysis_data if data.analysis_data else {}),
                ),
            )

        conn.commit()
        cur.close()
        conn.close()

        return fetch_all_interviews_by_position_id(position_id)
    except psycopg2.Error as e:
        print(f"Database error occurred while creating interview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"create_interview.Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while creating interview: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while creating interview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if conn:
            conn.close()


# Get all interviews by position ID
# This function fetches all interviews for a given position ID.
def fetch_all_interviews_by_position_id(position_id: str) -> List[Interview]:
    try:            
        conn = psycopg2.connect(settings.DATABASE_URL)
        print(f"Connected to database: {settings.DATABASE_URL}")
        cur = conn.cursor()
        cur.execute(
            """
            SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
            i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
            i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
            i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
            FROM interviews i 
            JOIN candidates_smarthr c ON i.candidate_id = c.id
            WHERE i.position_id::text=%s and c.is_deleted = false
            ORDER BY created_at DESC
            """,
            (position_id,),
        )
        rows = cur.fetchall()
        cur.close()
        conn.close()
        interviews = []
        for row in rows:
            position = get_position_by_id(str(row[1]))
            interviews.append(
                Interview(
                    id=str(row[0]),
                    position_id=str(row[1]),
                    candidate_id=str(row[2]),
                    candidate_info=row[19].get('personal_info', None),
                    position_info=position.position_info if position != None else None,
                    feedback_hr=row[3],
                    interview_date_hr=row[4],
                    feedback_date_hr=row[5],
                    status_hr=row[6],
                    recommendation_hr=row[7],
                    transcript_hr=row[8],
                    feedback_tec=row[9],
                    interview_date_tec=row[10],
                    feedback_date_tec=row[11],
                    status_tec=row[12],
                    recommendation_tec=row[13],
                    transcript_tec=row[14],
                    anwers_data=row[15],
                    interview_data=row[16],
                    created_at=row[17],
                    updated_at=row[18],
                    recruiter_hr_id=row[20],
                    scheduled_hr_id=row[21],
                    recruiter_tec_id=row[22],
                    scheduled_tec_id=row[23],
                    analysis_data=row[24]
                )
            )
        return interviews
    except psycopg2.Error as e:
        print(f"Database error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_all_interviews_by_position_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if conn:
            conn.close()


# Update interview feedback for HR
# This function updates the interview feedback for HR.
def update_interview_hr(interviewData: InterviewHr) -> Interview:
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()

    feedback = fetch_interview_by_position_id_candidate_id(interviewData.position_id, interviewData.candidate_id)
    if not feedback:
        raise HTTPException(status_code=404, detail="Interview not found")

    if feedback.status_hr == StatusInterview.COMPLETED.value or feedback.status_hr == StatusInterview.CANCELLED.value:
        raise HTTPException(status_code=400, detail="Interview already completed or cancelled")

    sqlQuery = """
         UPDATE interviews SET
            feedback_hr = %s,
            recruiter_hr_id = %s,
            scheduled_hr_id = %s,
            interview_date_hr = %s,
            feedback_date_hr = %s,
            status_hr = %s,
            recommendation_hr = %s,
            transcript_hr = %s,
            updated_at = Now()
            Where position_id = %s and candidate_id = %s
                RETURNING id,    position_id,    candidate_id,    feedback_hr,    interview_date_hr,    feedback_date_hr,    status_hr,
            recommendation_hr,    transcript_hr,    feedback_tec,    interview_date_tec,    feedback_date_tec,    status_tec,    recommendation_tec,
            transcript_tec,    created_at,    updated_at, recruiter_hr_id, scheduled_hr_id, recruiter_tec_id, scheduled_tec_id, analysis_data
            """
    params = [Json(interviewData.feedback_hr)]
    params.append(interviewData.recruiter_hr_id)
    params.append(interviewData.scheduled_hr_id)
    params.append(interviewData.interview_date_hr)
    params.append(interviewData.feedback_date_hr)
    params.append(interviewData.status_hr)
    params.append(interviewData.recommendation_hr)
    params.append(interviewData.transcript_hr)
    params.append(interviewData.position_id)
    params.append(interviewData.candidate_id)

    cur.execute(sqlQuery, params)
    row = cur.fetchone()
    conn.commit()
    cur.close()
    conn.close()

    if not row:
        return None

    return Interview(
        id=str(row[0]),
        position_id=str(row[1]),
        candidate_id=str(row[2]),
        feedback_hr=row[3],
        interview_date_hr=row[4],
        feedback_date_hr=row[5],
        status_hr=row[6],
        recommendation_hr=row[7],
        transcript_hr=row[8],
        feedback_tec=row[9],
        interview_date_tec=row[10],
        feedback_date_tec=row[11],
        status_tec=row[12],
        recommendation_tec=row[13],
        transcript_tec=row[14],
        created_at=row[15],
        updated_at=row[16],
        recruiter_hr_id=row[17],
        scheduled_hr_id=row[18],
        recruiter_tec_id=row[19],
        scheduled_tec_id=row[20],
        analysis_data=row[21]
    )


# Update interview feedback for the technical team
# This function updates the interview feedback for the technical team.
def update_interview_tec(interviewData: InterviewTec) -> Interview:
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()

    feedback = fetch_interview_by_position_id_candidate_id(interviewData.position_id, interviewData.candidate_id)
    if not feedback:
        raise HTTPException(status_code=404, detail="Interview not found (update_interview_tec)")

    if feedback.status_tec == StatusInterview.COMPLETED.value or feedback.status_tec == StatusInterview.CANCELLED.value:
        raise HTTPException(status_code=400, detail="Interview already completed or cancelled")

    sqlQuery = """
         UPDATE interviews SET            
            feedback_tec = %s,
            recruiter_tec_id = %s,
            scheduled_tec_id = %s,
            interview_date_tec = %s,
            feedback_date_tec = %s,
            status_tec = %s,
            recommendation_tec = %s,
            transcript_tec = %s,
            updated_at = Now()
            Where position_id = %s and candidate_id = %s
                RETURNING id,    position_id,    candidate_id,    feedback_hr,    interview_date_hr,    feedback_date_hr,    status_hr,
            recommendation_hr,    transcript_hr,    feedback_tec,    interview_date_tec,    feedback_date_tec,    status_tec,    recommendation_tec,
            transcript_tec,    created_at,    updated_at, recruiter_hr_id, scheduled_hr_id, recruiter_tec_id, scheduled_tec_id, analysis_data
            """
    params = [Json(interviewData.feedback_tec)]
    params.append(interviewData.recruiter_tec_id)
    params.append(interviewData.scheduled_tec_id)
    params.append(interviewData.interview_date_tec)
    params.append(interviewData.feedback_date_tec)
    params.append(interviewData.status_tec)
    params.append(interviewData.recommendation_tec)
    params.append(interviewData.transcript_tec)
    params.append(interviewData.position_id)
    params.append(interviewData.candidate_id)

    cur.execute(sqlQuery, params)
    row = cur.fetchone()
    conn.commit()
    cur.close()
    conn.close()

    if not row:
        return None

    response = Interview(
        id=str(row[0]),
        position_id=str(row[1]),
        candidate_id=str(row[2]),
        feedback_hr=row[3],
        interview_date_hr=row[4],
        feedback_date_hr=row[5],
        status_hr=row[6],
        recommendation_hr=row[7],
        transcript_hr=row[8],
        feedback_tec=row[9],
        interview_date_tec=row[10],
        feedback_date_tec=row[11],
        status_tec=row[12],
        recommendation_tec=row[13],
        transcript_tec=row[14],
        created_at=row[15],
        updated_at=row[16],
        recruiter_hr_id=row[17],
        scheduled_hr_id=row[18],
        recruiter_tec_id=row[19],
        scheduled_tec_id=row[20],
        analysis_data=row[21]
    )

    # fill anwers_data from transcript_tec after is completed
    if response.status_tec == StatusInterview.COMPLETED.value:
        run_and_persist_interview(response.id, ProcessType.EXTRACT)
        # evaluate: anwers_data against questionaire, and persist interview_data
        evaluate_interview(response.id)    

    return response 


# Get a single interview by position ID and candidate ID
# This function fetches a single interview for the given position and candidate IDs.
def fetch_interview_by_position_id_candidate_id(position_id: str, candidate_id: str) -> Optional[Interview]:
    try:            
        conn = psycopg2.connect(settings.DATABASE_URL)
        print(f"fetch_interview_by_position_id_candidate_id position_id: {position_id} and candidate_id: {candidate_id}")
        cur = conn.cursor()
        cur.execute(
            """
            SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
            i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
            i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info, 
            i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
            FROM interviews i 
            JOIN candidates_smarthr c ON i.candidate_id = c.id
            WHERE i.position_id::text=%s and i.candidate_id::text=%s and c.is_deleted = false
            ORDER BY created_at DESC
            """,
            (position_id, candidate_id,),
        )
        row = cur.fetchone()
        cur.close()
        conn.close()
        if not row:
            return None
        # print(f"Rows: {row}")
        position = get_position_by_id(str(row[1]))
        return Interview(
            id=str(row[0]),
            position_id=str(row[1]),
            candidate_id=str(row[2]),
            candidate_info=row[19].get('personal_info', None),
            position_info=position.position_info if position != None else None,
            feedback_hr=row[3],
            interview_date_hr=row[4],
            feedback_date_hr=row[5],
            status_hr=row[6],
            recommendation_hr=row[7],
            transcript_hr=row[8],
            feedback_tec=row[9],
            interview_date_tec=row[10],
            feedback_date_tec=row[11],
            status_tec=row[12],
            recommendation_tec=row[13],
            transcript_tec=row[14],
            anwers_data=row[15],
            interview_data=row[16],
            created_at=row[17],
            updated_at=row[18],
            recruiter_hr_id=row[20],
            scheduled_hr_id=row[21],
            recruiter_tec_id=row[22],
            scheduled_tec_id=row[23],
            analysis_data=row[24]
        )
    except psycopg2.Error as e:
        print(f"Database error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interview_by_position_id_candidate_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if conn:
            conn.close()


# Get questions by position ID
# This function fetches the questions for the given position ID.
def fetch_questions_by_position_id(position_id: str) -> SingleQuestions:
    try:            
        conn = psycopg2.connect(settings.DATABASE_URL)
        print(f"Connected to database:: {settings.DATABASE_URL}")
        cur = conn.cursor()
        # Fetch the questions for the given position_id
        # Use the correct SQL query to fetch the questions
        sqlQuery = """
            SELECT id, position_id, data, created_at, updated_at, allow_regeneration, created_by, updated_by
            FROM interview_questions WHERE position_id::text=%s
            """
        params = (position_id,)
        cur.execute(sqlQuery, params)
        row = cur.fetchone()
        # check if the row is empty
        if not row:
            raise HTTPException(status_code=404, detail="Questions not found")
        conn.commit()
        cur.close()
        conn.close()
        # Create a SingleQuestions object from the row data
        # Assuming the data is in the correct format, you can directly use it
        return SingleQuestions(
            id=str(row[0]),
            position_id=str(row[1]),
            data=row[2],
            created_at=row[3],
            updated_at=row[4],
            allow_regeneration=row[5],
            created_by=row[6],
            updated_by=row[7]
        )
    except psycopg2.Error as e:
        print(f"Database error occurred while fetch_questions_by_position_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_questions_by_position_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetch_questions_by_position_id: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetch_questions_by_position_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if conn:
            conn.close()


# Delete interview by position ID and candidate ID
# This function deletes an interview for the given position and candidate IDs.
def delete_interview(position_id: str, candidate_id: str) -> bool:
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()
        # Fetch the interview to check its status
        feedback = fetch_interview_by_position_id_candidate_id(position_id, candidate_id)
        if not feedback:
            raise HTTPException(status_code=404, detail="Interview not found")
        """ if feedback.status_hr == StatusInterview.IN_PROGRESS.value or feedback.status_hr == StatusInterview.COMPLETED.value or feedback.status_hr == StatusInterview.CANCELLED.value:
            raise HTTPException(status_code=400, detail="Interview already completed, in progress or cancelled") """
        # Check if the interview is in progress or completed
        # If it is, raise an error
        if feedback.status_tec == StatusInterview.IN_PROGRESS.value or feedback.status_tec == StatusInterview.COMPLETED.value:
            raise HTTPException(status_code=400, detail="Interview already completed or in progress")

        cur.execute(
            """
            DELETE FROM interviews WHERE id = %s
            """,
            (feedback.id,),
        )
        conn.commit()
        cur.close()
        conn.close()
        return True
    except psycopg2.Error as e:
        print(f"Database error occurred while deleting interview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"delete_interview. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while deleting interview: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while deleting interview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if conn:
            conn.close()


# Get all interviews by candidate ID
# This function fetches all interviews for the given candidate ID.
def fetch_interviews_by_candidate_id(candidate_id: str) -> List[Interview]:
    try:            
        conn = psycopg2.connect(settings.DATABASE_URL)
        print(f"fetch_interviews_by_candidate_id: {candidate_id}")
        cur = conn.cursor()
        cur.execute(
            """
            SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
            i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
            i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
            i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
            FROM interviews i 
            JOIN candidates_smarthr c ON i.candidate_id = c.id
            WHERE i.candidate_id::text=%s and c.is_deleted = false
            ORDER BY created_at DESC
            """,
            (candidate_id,),
        )
        rows = cur.fetchall()
        cur.close()
        conn.close()
        # print(f"Rows: {rows}")
        interviews: List[Interview] = []
        for row in rows:
            position = get_position_by_id(str(row[1]))
            interviews.append(
                Interview(
                    id=str(row[0]),
                    position_id=str(row[1]),
                    candidate_id=str(row[2]),
                    candidate_info=row[19].get('personal_info', None),
                    position_info=position.position_info if position != None else None,
                    feedback_hr=row[3],
                    interview_date_hr=row[4],
                    feedback_date_hr=row[5],
                    status_hr=row[6],
                    recommendation_hr=row[7],
                    transcript_hr=row[8],
                    feedback_tec=row[9],
                    interview_date_tec=row[10],
                    feedback_date_tec=row[11],
                    status_tec=row[12],
                    recommendation_tec=row[13],
                    transcript_tec=row[14],
                    anwers_data=row[15],
                    interview_data=row[16],
                    created_at=row[17],
                    updated_at=row[18],
                    recruiter_hr_id=row[20],
                    scheduled_hr_id=row[21],
                    recruiter_tec_id=row[22],
                    scheduled_tec_id=row[23],
                    analysis_data=row[24]
                )
            )
        return interviews
    except psycopg2.Error as e:
        print(f"Database error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interviews_by_candidate_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetch_interviews_by_candidate_id: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if conn:
            conn.close()


# Fetch interview by interview ID
# This function fetches an interview by its ID.
def fetch_interview_by_interview_id(interview_id: str) -> Optional[Interview]:
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        print(f"fetch_interview_by_interview_id: {interview_id}")
        cur = conn.cursor()
        cur.execute(
            """
            SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
            i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
            i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
            i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
            FROM interviews i 
            JOIN candidates_smarthr c ON i.candidate_id = c.id
            WHERE i.id::text=%s and c.is_deleted = false
            ORDER BY created_at DESC
            """,
            (interview_id,),
        )
        row = cur.fetchone()
        cur.close()
        conn.close()
        print(f"Rows: {row}")
        if not row:
            return None
        return Interview(
            id=str(row[0]),
            position_id=str(row[1]),
            candidate_id=str(row[2]),
            candidate_info=None,
            position_info=None,
            feedback_hr=row[3],
            interview_date_hr=row[4],
            feedback_date_hr=row[5],
            status_hr=row[6],
            recommendation_hr=row[7],
            transcript_hr=row[8],
            feedback_tec=row[9],
            interview_date_tec=row[10],
            feedback_date_tec=row[11],
            status_tec=row[12],
            recommendation_tec=row[13],
            transcript_tec=row[14],
            anwers_data=row[15],
            interview_data=row[16],
            created_at=row[17],
            updated_at=row[18],
            recruiter_hr_id=row[20],
            scheduled_hr_id=row[21],
            recruiter_tec_id=row[22],
            scheduled_tec_id=row[23],
            analysis_data=row[24]
        )
    except psycopg2.Error as e:
        print(f"Database error occurred while fetch_interview_by_interview_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interview_by_interview_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetch_interview_by_interview_id: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetch_interview_by_interview_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if conn:
            conn.close()


# Re-evaluate an interview by its ID
# This function re-evaluates an interview by its ID.
# It fetches the interview data, extract answers data if needed, evaluates it, and then fetches the updated interview data.
def re_evalute_interview(interview_id: str) -> Interview:
    # 1. Fetch the interview data
    interview = fetch_interview_by_interview_id(interview_id)
    if not interview:
        raise HTTPException(status_code=404, detail="Interview not found (re_evalute_interview)")

    if not interview.transcript_tec:
        raise HTTPException(status_code=400, detail="Technical Transcript not found")

    if not interview.anwers_data:
        # 2. Run and persist the interview
        run_and_persist_interview(interview.id, ProcessType.EXTRACT)

    # 3. Evaluate the interview
    evaluate_interview(interview.id)

    # 4. Fetch the updated interview data
    interview = fetch_interview_by_interview_id(interview.id)
    return interview


# Change Questions status
def change_question_status(position_id: str, question_id: str, allow_regeneration: bool) -> bool:
    """
    Change the status of a question in the interview_questions table.
    :param position_id: The ID of the position to which the question belongs.
    :param question_id: The ID of the question to update.
    :param allow_regeneration: Boolean indicating whether regeneration is allowed.
    :return: True if the update was successful, False otherwise.
    """
    with get_cursor() as cur:
        cur.execute(
            """
            UPDATE interview_questions
            SET allow_regeneration = %s,
                updated_at = NOW()
            WHERE id = %s AND position_id = %s;
            """,
            (allow_regeneration, question_id, position_id)
        )
        if cur.rowcount == 0:
            raise HTTPException(status_code=404, detail="Question not found")
        return True
    return False
