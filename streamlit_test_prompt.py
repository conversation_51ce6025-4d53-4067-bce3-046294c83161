#!/usr/bin/env python3
"""
Interfaz Streamlit para probar la mejora del prompt de evaluación de entrevistas.
Permite ingresar comentarios personalizados y comparar resultados de evaluación.
"""

import streamlit as st
import json
from datetime import datetime
import random

# Configuración de la página
st.set_page_config(
    page_title="Test Prompt Mejora - Evaluación Entrevistas",
    page_icon="🎯",
    layout="wide"
)

st.title("🎯 Test de Mejora del Prompt de Evaluación")
st.markdown("**Ingresa comentarios personalizados y compara los resultados de evaluación**")

# Sidebar para configuración
st.sidebar.header("⚙️ Configuración")
test_mode = st.sidebar.selectbox(
    "Selecciona el modo de prueba:",
    ["Evaluación Interactiva", "Comparación de Prompts", "Solo Prompt Anterior", "Solo Prompt Mejorado"]
)

# Datos de ejemplo
st.sidebar.header("📝 Datos de Prueba")
use_sample_data = st.sidebar.checkbox("Usar datos de ejemplo", value=True)

if use_sample_data:
    # Datos de ejemplo predefinidos
    sample_questions = {
        "questions": [
            {
                "question": "¿Cuál es la diferencia entre REST y GraphQL?",
                "senior_answer": "REST usa múltiples endpoints con verbos HTTP, GraphQL usa un endpoint único con queries flexibles. GraphQL permite solicitar exactamente lo necesario, reduciendo over-fetching. REST tiene mejor caching y es más simple para CRUD básico.",
                "mid_answer": "REST usa diferentes URLs para recursos, GraphQL usa una URL pero puedes pedir datos específicos. GraphQL es más flexible pero REST es más fácil de entender.",
                "junior_answer": "REST es para obtener datos del servidor, GraphQL es más nuevo y te permite obtener exactamente lo que quieres."
            }
        ]
    }
    
    sample_answers = {
        "answers": [
            "Bueno, las APIs REST usan diferentes endpoints para diferentes recursos, como /users o /posts. GraphQL es diferente porque usa solo un endpoint pero puedes especificar exactamente qué datos quieres en tu query. He usado ambos en mis proyectos - REST para operaciones CRUD simples y GraphQL cuando necesitaba fetching de datos más complejo con relaciones."
        ]
    }
    
    sample_comments = {
        "habilidades_tecnicas": "Fuerte comprensión de arquitecturas de API. El candidato demostró experiencia práctica con REST y GraphQL.",
        "comunicacion": "Explicaciones claras con buenos ejemplos de proyectos reales.",
        "profundidad_conocimiento": "Buen entendimiento de cuándo usar cada enfoque, muestra pensamiento práctico.",
        "areas_mejora": "Podría profundizar más en las desventajas de cada enfoque."
    }
    
    sample_transcript = """
Entrevistador: ¿Puedes explicar la diferencia entre APIs REST y GraphQL?

Candidato: Bueno, las APIs REST usan diferentes endpoints para diferentes recursos, como /users o /posts. GraphQL es diferente porque usa solo un endpoint pero puedes especificar exactamente qué datos quieres en tu query. He usado ambos en mis proyectos - REST para operaciones CRUD simples y GraphQL cuando necesitaba fetching de datos más complejo con relaciones.

Entrevistador: Buen inicio. ¿Puedes contarme sobre desafíos que hayas enfrentado con cualquiera de los dos enfoques?

Candidato: Con REST, a veces tenía que hacer múltiples llamadas API para obtener datos relacionados, lo cual no era eficiente. Con GraphQL, la curva de aprendizaje fue más empinada inicialmente, y el caching era más complejo comparado con REST.

Entrevistador: Interesante. ¿En qué situaciones elegirías uno sobre el otro?

Candidato: Elegiría REST para aplicaciones simples con operaciones CRUD directas, especialmente cuando el caching es importante. GraphQL lo usaría cuando necesito flexibilidad en las queries y tengo relaciones complejas entre datos.
"""

else:
    # Permitir entrada manual
    st.sidebar.markdown("**Datos Personalizados:**")
    sample_questions = {"questions": [{"question": "", "senior_answer": "", "mid_answer": "", "junior_answer": ""}]}
    sample_answers = {"answers": [""]}
    sample_comments = {}
    sample_transcript = ""

# Simulador de LLM para demostración
def simulate_old_llm_evaluation(context_data):
    """Simula la evaluación con el prompt anterior (sin integración)"""
    # Solo usa expected y actual, ignora comentarios y transcript
    expected = context_data.get('expected', {})
    actual = context_data.get('actual', {})

    # Simulación básica sin integración
    return {
        "per_question": [
            {
                "question_number": 1,
                "detected_seniority": "mid",
                "explanation": "La respuesta muestra conocimiento técnico básico de REST y GraphQL. Menciona conceptos clave pero sin profundidad en trade-offs o casos de uso específicos."
            }
        ],
        "overall_seniority": "mid",
        "percentage_of_match": 65.0,
        "explanation": "Evaluación basada únicamente en respuestas extraídas. El candidato demuestra comprensión técnica intermedia pero falta análisis de contexto adicional."
    }

def simulate_new_llm_evaluation(context_data):
    """Simula la evaluación con el prompt mejorado (con integración)"""
    expected = context_data.get('expected', {})
    actual = context_data.get('actual', {})
    feedback_comments = context_data.get('feedback_comments', {})
    transcript = context_data.get('transcript_excerpt', '')

    # Simulación con integración de comentarios y transcript
    integration_notes = []

    if feedback_comments:
        integration_notes.append("✅ Comentarios del experto integrados")
        if 'habilidades_tecnicas' in feedback_comments:
            integration_notes.append(f"• Habilidades técnicas: {feedback_comments['habilidades_tecnicas']}")

    if transcript:
        integration_notes.append("✅ Contexto del transcript analizado")
        if 'proyectos' in transcript.lower():
            integration_notes.append("• Evidencia de experiencia práctica encontrada en transcript")

    explanation = "Evaluación integrada basada en múltiples fuentes: " + "; ".join(integration_notes)

    # Ajustar seniority basado en comentarios
    seniority = "senior" if feedback_comments and "fuerte" in str(feedback_comments).lower() else "mid"
    percentage = 85.0 if feedback_comments else 65.0

    return {
        "per_question": [
            {
                "question_number": 1,
                "detected_seniority": seniority,
                "explanation": f"Análisis integrado: Los comentarios del experto confirman '{feedback_comments.get('habilidades_tecnicas', 'N/A')}' y el transcript muestra evidencia práctica con 'he usado ambos en mis proyectos'. Esta combinación indica nivel {seniority}."
            }
        ],
        "overall_seniority": seniority,
        "percentage_of_match": percentage,
        "explanation": explanation
    }

# Definir los prompts
OLD_PROMPT = """
Compare candidate answers with expected senior/mid/junior answers.
For every question produce detected_seniority (senior|mid|junior) and an explanation.
Also output overall_seniority.
Return JSON that matches the provided schema.
IMPORTANT RULES:
• Provide an overall_seniority (senior|mid|junior) based on the comments, transcript, and candidate info.
• Include an explanation: Overall justification of the evaluation.
• Ensure the output is a valid JSON object with correct structure and syntax.
"""

NEW_PROMPT = """
Evaluate the candidate's responses against expected senior/mid/junior levels by integrating all available information.
For each question, identify 'detected_seniority' (senior|mid|junior) and provide an explanation.
Additionally, determine the 'overall_seniority' and calculate the 'percentage_of_match'.
Deliver a JSON output conforming to the specified schema.
CRITICAL INTEGRATION REQUIREMENTS:
• PRIORITIZE expert manual feedback comments where available, as they offer critical human insights.
• Utilize the transcript to understand the conversation flow and the candidate's reasoning.
• Cross-reference responses with feedback comments to uncover patterns and insights.
• Link specific strengths/weaknesses noted in feedback comments to supporting evidence in the Q&A.
• Acknowledge and explain discrepancies between comments and extracted answers.
• Conclude with an 'overall_seniority' based on a holistic analysis of all data sources.
• Include a detailed explanation of how comments, transcript, and answers were integrated.
• Ensure the output is a valid JSON object with the correct structure and syntax.
"""

# Función para mostrar el análisis
def show_prompt_analysis(prompt_type, prompt_text, context_data):
    st.subheader(f"📋 {prompt_type}")
    
    # Mostrar el prompt
    with st.expander("Ver Prompt Completo", expanded=False):
        st.code(prompt_text, language="text")
    
    # Mostrar datos de contexto
    st.markdown("**Datos enviados al LLM:**")
    context_keys = list(context_data.keys())
    
    col1, col2 = st.columns(2)
    with col1:
        st.markdown("**Claves disponibles:**")
        for key in context_keys:
            if key in ['feedback_comments', 'transcript_excerpt']:
                st.markdown(f"✅ `{key}` - **NUEVO**")
            else:
                st.markdown(f"📝 `{key}`")
    
    with col2:
        st.markdown("**Capacidades de integración:**")
        if 'feedback_comments' in context_keys:
            st.markdown("✅ Puede usar comentarios del experto")
        else:
            st.markdown("❌ Sin acceso a comentarios")
            
        if 'transcript_excerpt' in context_keys:
            st.markdown("✅ Puede usar contexto del transcript")
        else:
            st.markdown("❌ Sin contexto del transcript")
    
    # Mostrar muestra de datos
    with st.expander("Ver Datos de Contexto", expanded=False):
        st.json(context_data)
    
    return context_data

# Layout principal
if test_mode == "Evaluación Interactiva":
    st.header("🎯 Evaluación Interactiva")
    st.markdown("**Ingresa tus propios comentarios y ve cómo cada prompt los integra**")

    # Sección de entrada de datos
    st.subheader("📝 Datos de la Entrevista")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**Pregunta de la Entrevista:**")
        question = st.text_area(
            "Pregunta técnica:",
            value="¿Cuál es la diferencia entre REST y GraphQL?",
            height=100
        )

        st.markdown("**Respuesta del Candidato:**")
        candidate_answer = st.text_area(
            "Respuesta extraída del transcript:",
            value="Las APIs REST usan diferentes endpoints para recursos, GraphQL usa un endpoint único pero puedes especificar qué datos quieres. He usado ambos en proyectos - REST para CRUD simple y GraphQL para fetching complejo con relaciones.",
            height=120
        )

    with col2:
        st.markdown("**🔥 Tus Comentarios (Aquí puedes experimentar):**")

        technical_skills = st.text_input(
            "Habilidades Técnicas:",
            value="Fuerte comprensión de arquitecturas de API"
        )

        communication = st.text_input(
            "Comunicación:",
            value="Explicaciones claras con ejemplos prácticos"
        )

        depth = st.text_input(
            "Profundidad del Conocimiento:",
            value="Buen entendimiento de casos de uso"
        )

        areas_improvement = st.text_input(
            "Áreas de Mejora:",
            value="Podría profundizar en trade-offs"
        )

    st.markdown("**Transcript de la Conversación:**")
    transcript_input = st.text_area(
        "Contexto completo de la entrevista:",
        value=sample_transcript,
        height=150
    )

    # Botón para ejecutar evaluación
    if st.button("🚀 Ejecutar Evaluación Comparativa", type="primary"):

        # Preparar datos
        user_questions = {"questions": [{"question": question}]}
        user_answers = {"answers": [candidate_answer]}
        user_comments = {
            "habilidades_tecnicas": technical_skills,
            "comunicacion": communication,
            "profundidad_conocimiento": depth,
            "areas_mejora": areas_improvement
        }

        st.subheader("📊 Resultados de Evaluación")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("### ❌ Prompt Anterior")
            st.markdown("*Solo usa preguntas y respuestas*")

            old_context = {
                'expected': user_questions,
                'actual': user_answers
            }

            old_result = simulate_old_llm_evaluation(old_context)

            st.markdown("**Resultado:**")
            st.json(old_result)

            st.markdown("**Limitaciones observadas:**")
            st.error("❌ No usa tus comentarios")
            st.error("❌ No considera el transcript completo")
            st.error("❌ Evaluación superficial")

        with col2:
            st.markdown("### ✅ Prompt Mejorado")
            st.markdown("*Integra comentarios + transcript*")

            new_context = {
                'expected': user_questions,
                'actual': user_answers,
                'feedback_comments': user_comments,
                'transcript_excerpt': transcript_input[:1000] + "..." if len(transcript_input) > 1000 else transcript_input
            }

            new_result = simulate_new_llm_evaluation(new_context)

            st.markdown("**Resultado:**")
            st.json(new_result)

            st.markdown("**Mejoras observadas:**")
            st.success("✅ Usa tus comentarios como base")
            st.success("✅ Integra evidencia del transcript")
            st.success("✅ Evaluación más completa")

        # Comparación directa
        st.subheader("🔍 Comparación Directa")

        comparison_col1, comparison_col2, comparison_col3 = st.columns(3)

        with comparison_col1:
            st.metric(
                "Seniority - Anterior",
                old_result['overall_seniority'],
                delta=None
            )
            st.metric(
                "Match % - Anterior",
                f"{old_result['percentage_of_match']}%",
                delta=None
            )

        with comparison_col2:
            st.metric(
                "Seniority - Mejorado",
                new_result['overall_seniority'],
                delta="Upgrade" if new_result['overall_seniority'] != old_result['overall_seniority'] else None
            )
            st.metric(
                "Match % - Mejorado",
                f"{new_result['percentage_of_match']}%",
                delta=f"+{new_result['percentage_of_match'] - old_result['percentage_of_match']}%" if new_result['percentage_of_match'] > old_result['percentage_of_match'] else None
            )

        with comparison_col3:
            st.markdown("**Diferencias Clave:**")
            if new_result['overall_seniority'] != old_result['overall_seniority']:
                st.success(f"🎯 Seniority mejorado: {old_result['overall_seniority']} → {new_result['overall_seniority']}")

            if new_result['percentage_of_match'] > old_result['percentage_of_match']:
                st.success(f"📈 Match mejorado: +{new_result['percentage_of_match'] - old_result['percentage_of_match']}%")

            st.info("💡 El prompt mejorado usa TUS comentarios para una evaluación más precisa")

elif test_mode == "Comparación de Prompts":
    st.header("🔄 Comparación de Prompts")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("### ❌ Prompt Anterior")
        old_context = {
            'expected': sample_questions,
            'actual': sample_answers
        }
        show_prompt_analysis("Prompt Anterior", OLD_PROMPT, old_context)

    with col2:
        st.markdown("### ✅ Prompt Mejorado")
        new_context = {
            'expected': sample_questions,
            'actual': sample_answers,
            'feedback_comments': sample_comments,
            'transcript_excerpt': sample_transcript[:1000] + "..." if len(sample_transcript) > 1000 else sample_transcript
        }
        show_prompt_analysis("Prompt Mejorado", NEW_PROMPT, new_context)

elif test_mode == "Solo Prompt Anterior":
    st.header("❌ Prompt Anterior")
    old_context = {
        'expected': sample_questions,
        'actual': sample_answers
    }
    show_prompt_analysis("Prompt Anterior", OLD_PROMPT, old_context)

else:  # Solo Prompt Mejorado
    st.header("✅ Prompt Mejorado")
    new_context = {
        'expected': sample_questions,
        'actual': sample_answers,
        'feedback_comments': sample_comments,
        'transcript_excerpt': sample_transcript[:1000] + "..." if len(sample_transcript) > 1000 else sample_transcript
    }
    show_prompt_analysis("Prompt Mejorado", NEW_PROMPT, new_context)

# Información adicional
if test_mode != "Evaluación Interactiva":
    st.header("🔗 Cómo Funciona la Integración")
    st.markdown("**El prompt mejorado combina los datos de esta manera:**")

    demo_col1, demo_col2 = st.columns(2)

    with demo_col1:
        st.markdown("**📝 Comentario del Experto:**")
        st.info("'Fuerte comprensión de arquitecturas de API'")

        st.markdown("**🎯 Evidencia en Transcript:**")
        st.success("Candidato explicó endpoints REST y endpoint único GraphQL")

    with demo_col2:
        st.markdown("**📝 Comentario del Experto:**")
        st.info("'Experiencia práctica con REST y GraphQL'")

        st.markdown("**🎯 Evidencia en Transcript:**")
        st.success("'He usado ambos en mis proyectos'")

    st.markdown("**🧠 Proceso de Integración:**")
    st.markdown("""
    1. **Prioriza** las observaciones del experto (tus comentarios)
    2. **Busca evidencia** en el transcript que respalde cada comentario
    3. **Crea conexiones** entre lo observado y lo dicho
    4. **Explica la metodología** de integración en la evaluación
    """)

# Footer
st.markdown("---")
st.markdown("**💡 Prueba la 'Evaluación Interactiva' para experimentar con tus propios comentarios y ver la diferencia en tiempo real!**")
