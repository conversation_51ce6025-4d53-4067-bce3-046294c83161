#!/usr/bin/env python3
"""
Interfaz Streamlit para probar la mejora del prompt de evaluación de entrevistas.
Permite comparar el prompt anterior vs el mejorado.
"""

import streamlit as st
import json
from datetime import datetime

# Configuración de la página
st.set_page_config(
    page_title="Test Prompt Mejora - Evaluación Entrevistas",
    page_icon="🎯",
    layout="wide"
)

st.title("🎯 Test de Mejora del Prompt de Evaluación")
st.markdown("**Compara cómo el prompt anterior vs el mejorado integra transcript + comentarios**")

# Sidebar para configuración
st.sidebar.header("⚙️ Configuración")
test_mode = st.sidebar.selectbox(
    "Selecciona el modo de prueba:",
    ["Comparación Lado a Lado", "Prompt Anterior", "Prompt Mejorado"]
)

# Datos de ejemplo
st.sidebar.header("📝 Datos de Prueba")
use_sample_data = st.sidebar.checkbox("Usar datos de ejemplo", value=True)

if use_sample_data:
    # Datos de ejemplo predefinidos
    sample_questions = {
        "questions": [
            {
                "question": "¿Cuál es la diferencia entre REST y GraphQL?",
                "senior_answer": "REST usa múltiples endpoints con verbos HTTP, GraphQL usa un endpoint único con queries flexibles. GraphQL permite solicitar exactamente lo necesario, reduciendo over-fetching. REST tiene mejor caching y es más simple para CRUD básico.",
                "mid_answer": "REST usa diferentes URLs para recursos, GraphQL usa una URL pero puedes pedir datos específicos. GraphQL es más flexible pero REST es más fácil de entender.",
                "junior_answer": "REST es para obtener datos del servidor, GraphQL es más nuevo y te permite obtener exactamente lo que quieres."
            }
        ]
    }
    
    sample_answers = {
        "answers": [
            "Bueno, las APIs REST usan diferentes endpoints para diferentes recursos, como /users o /posts. GraphQL es diferente porque usa solo un endpoint pero puedes especificar exactamente qué datos quieres en tu query. He usado ambos en mis proyectos - REST para operaciones CRUD simples y GraphQL cuando necesitaba fetching de datos más complejo con relaciones."
        ]
    }
    
    sample_comments = {
        "habilidades_tecnicas": "Fuerte comprensión de arquitecturas de API. El candidato demostró experiencia práctica con REST y GraphQL.",
        "comunicacion": "Explicaciones claras con buenos ejemplos de proyectos reales.",
        "profundidad_conocimiento": "Buen entendimiento de cuándo usar cada enfoque, muestra pensamiento práctico.",
        "areas_mejora": "Podría profundizar más en las desventajas de cada enfoque."
    }
    
    sample_transcript = """
Entrevistador: ¿Puedes explicar la diferencia entre APIs REST y GraphQL?

Candidato: Bueno, las APIs REST usan diferentes endpoints para diferentes recursos, como /users o /posts. GraphQL es diferente porque usa solo un endpoint pero puedes especificar exactamente qué datos quieres en tu query. He usado ambos en mis proyectos - REST para operaciones CRUD simples y GraphQL cuando necesitaba fetching de datos más complejo con relaciones.

Entrevistador: Buen inicio. ¿Puedes contarme sobre desafíos que hayas enfrentado con cualquiera de los dos enfoques?

Candidato: Con REST, a veces tenía que hacer múltiples llamadas API para obtener datos relacionados, lo cual no era eficiente. Con GraphQL, la curva de aprendizaje fue más empinada inicialmente, y el caching era más complejo comparado con REST.

Entrevistador: Interesante. ¿En qué situaciones elegirías uno sobre el otro?

Candidato: Elegiría REST para aplicaciones simples con operaciones CRUD directas, especialmente cuando el caching es importante. GraphQL lo usaría cuando necesito flexibilidad en las queries y tengo relaciones complejas entre datos.
"""

else:
    # Permitir entrada manual
    st.sidebar.markdown("**Datos Personalizados:**")
    sample_questions = {"questions": [{"question": "", "senior_answer": "", "mid_answer": "", "junior_answer": ""}]}
    sample_answers = {"answers": [""]}
    sample_comments = {}
    sample_transcript = ""

# Definir los prompts
OLD_PROMPT = """
Compare candidate answers with expected senior/mid/junior answers. 
For every question produce detected_seniority (senior|mid|junior) and an explanation. 
Also output overall_seniority. 
Return JSON that matches the provided schema.
IMPORTANT RULES:
• Provide an overall_seniority (senior|mid|junior) based on the comments, transcript, and candidate info.
• Include an explanation: Overall justification of the evaluation.
• Ensure the output is a valid JSON object with correct structure and syntax.
"""

NEW_PROMPT = """
Evaluate the candidate's responses against expected senior/mid/junior levels by integrating all available information. 
For each question, identify 'detected_seniority' (senior|mid|junior) and provide an explanation. 
Additionally, determine the 'overall_seniority' and calculate the 'percentage_of_match'. 
Deliver a JSON output conforming to the specified schema.
CRITICAL INTEGRATION REQUIREMENTS:
• PRIORITIZE expert manual feedback comments where available, as they offer critical human insights.
• Utilize the transcript to understand the conversation flow and the candidate's reasoning.
• Cross-reference responses with feedback comments to uncover patterns and insights.
• Link specific strengths/weaknesses noted in feedback comments to supporting evidence in the Q&A.
• Acknowledge and explain discrepancies between comments and extracted answers.
• Conclude with an 'overall_seniority' based on a holistic analysis of all data sources.
• Include a detailed explanation of how comments, transcript, and answers were integrated.
• Ensure the output is a valid JSON object with the correct structure and syntax.
"""

# Función para mostrar el análisis
def show_prompt_analysis(prompt_type, prompt_text, context_data):
    st.subheader(f"📋 {prompt_type}")
    
    # Mostrar el prompt
    with st.expander("Ver Prompt Completo", expanded=False):
        st.code(prompt_text, language="text")
    
    # Mostrar datos de contexto
    st.markdown("**Datos enviados al LLM:**")
    context_keys = list(context_data.keys())
    
    col1, col2 = st.columns(2)
    with col1:
        st.markdown("**Claves disponibles:**")
        for key in context_keys:
            if key in ['feedback_comments', 'transcript_excerpt']:
                st.markdown(f"✅ `{key}` - **NUEVO**")
            else:
                st.markdown(f"📝 `{key}`")
    
    with col2:
        st.markdown("**Capacidades de integración:**")
        if 'feedback_comments' in context_keys:
            st.markdown("✅ Puede usar comentarios del experto")
        else:
            st.markdown("❌ Sin acceso a comentarios")
            
        if 'transcript_excerpt' in context_keys:
            st.markdown("✅ Puede usar contexto del transcript")
        else:
            st.markdown("❌ Sin contexto del transcript")
    
    # Mostrar muestra de datos
    with st.expander("Ver Datos de Contexto", expanded=False):
        st.json(context_data)
    
    return context_data

# Layout principal
if test_mode == "Comparación Lado a Lado":
    st.header("🔄 Comparación Lado a Lado")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### ❌ Prompt Anterior")
        old_context = {
            'expected': sample_questions,
            'actual': sample_answers
        }
        show_prompt_analysis("Prompt Anterior", OLD_PROMPT, old_context)
        
        st.markdown("**Problemas identificados:**")
        st.markdown("- Solo usa preguntas esperadas y respuestas extraídas")
        st.markdown("- No tiene acceso a comentarios del entrevistador")
        st.markdown("- No puede usar el contexto completo del transcript")
        st.markdown("- Menciona 'comments' en el prompt pero no los recibe")
    
    with col2:
        st.markdown("### ✅ Prompt Mejorado")
        new_context = {
            'expected': sample_questions,
            'actual': sample_answers,
            'feedback_comments': sample_comments,
            'transcript_excerpt': sample_transcript[:1000] + "..." if len(sample_transcript) > 1000 else sample_transcript
        }
        show_prompt_analysis("Prompt Mejorado", NEW_PROMPT, new_context)
        
        st.markdown("**Mejoras implementadas:**")
        st.markdown("- ✅ Incluye comentarios del experto como datos")
        st.markdown("- ✅ Proporciona excerpt del transcript")
        st.markdown("- ✅ Instrucciones específicas de integración")
        st.markdown("- ✅ Prioriza observaciones humanas expertas")

elif test_mode == "Prompt Anterior":
    st.header("❌ Prompt Anterior")
    old_context = {
        'expected': sample_questions,
        'actual': sample_answers
    }
    show_prompt_analysis("Prompt Anterior", OLD_PROMPT, old_context)

else:  # Prompt Mejorado
    st.header("✅ Prompt Mejorado")
    new_context = {
        'expected': sample_questions,
        'actual': sample_answers,
        'feedback_comments': sample_comments,
        'transcript_excerpt': sample_transcript[:1000] + "..." if len(sample_transcript) > 1000 else sample_transcript
    }
    show_prompt_analysis("Prompt Mejorado", NEW_PROMPT, new_context)

# Sección de demostración de integración
st.header("🔗 Demostración de Integración")
st.markdown("**Cómo el prompt mejorado combinaría los datos:**")

demo_col1, demo_col2 = st.columns(2)

with demo_col1:
    st.markdown("**📝 Comentario del Experto:**")
    st.info("'Fuerte comprensión de arquitecturas de API'")
    
    st.markdown("**🎯 Evidencia en Transcript:**")
    st.success("Candidato explicó endpoints REST y endpoint único GraphQL con detalles técnicos")

with demo_col2:
    st.markdown("**📝 Comentario del Experto:**")
    st.info("'Experiencia práctica con ambos REST y GraphQL'")
    
    st.markdown("**🎯 Evidencia en Transcript:**")
    st.success("'He usado ambos en mis proyectos - REST para CRUD simples y GraphQL para fetching complejo'")

st.markdown("**🧠 Resultado de Integración:**")
st.markdown("""
El prompt mejorado instruye al LLM a:
1. **Comenzar** con las observaciones del experto como insights primarios
2. **Buscar evidencia** específica en el transcript que respalde cada comentario
3. **Crear conexiones lógicas** entre lo observado y lo dicho
4. **Explicar la metodología** de integración en la evaluación final
""")

# Footer
st.markdown("---")
st.markdown("**💡 Conclusión:** El prompt mejorado resuelve el problema de 'no siempre combina bien las preguntas y respuestas del transcript con los comentarios' proporcionando instrucciones explícitas y todos los datos necesarios para una integración efectiva.")
