# 1. Standard library imports
import io
from typing import List

# 2. Third-party imports
import psycopg2
from fastapi import (
    APIRouter, Depends, File, HTTPException, UploadFile
)
from fastapi.responses import StreamingResponse
import logging
from opentelemetry import trace
from opentelemetry.propagate import extract  # (Optional, if you want to extract context from a header)

# 3. Internal imports
from utils.auth_bearer import J<PERSON><PERSON><PERSON>earer
from utils.jwt_utils import validate_token
from controllers.candidates_controller import (
    candidate_match_positions,
    get_countries,
    get_job_titles,
    update_all_candidates,
    validate_candidate,
    change_candidate_status,
    create_candidate,
    fetch_all_candidates,
    get_candidate_by_id,
    get_candidates_page,
    get_total_candidates,
    update_candidate,
    create_candidate_pdf,
    create_candidate_docx,
    delete_candidate,
    get_candidate_by_email
)
from controllers.project_controller import get_project_by_id
from models.validate_models import ValidateCandidateRequest, ValidateCandidateResponse
from models.responses import PaginationResponse
from models.candidate import (
    Candidate, Candidate<PERSON>reate, CandidateFilt<PERSON>, Candidate<PERSON><PERSON><PERSON>, UploadFileResponse
)
from utils.email_utils import verify_emails, is_valid_email
from utils.file_utils import get_filename
from utils.lumus_utils import extract_data_lumus

# Logging configuration
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__)
router = APIRouter()


# Create candidate
# This endpoint creates a new candidate based on the provided candidate information
# It requires an Authorization header with a Bearer token for authentication
@router.post("/", response_model=Candidate, dependencies=[Depends(JWTBearer())])
def create_candidate_endpoint(candidate: CandidateCreate):
    """
    Create a new candidate based on the provided candidate information.

    Args:
        candidate (CandidateCreate): The candidate data to create.

    Returns:
        Candidate: The created candidate object.

    Raises:
        HTTPException: If required fields are missing or candidate already exists.
    """
    try:
        logger.info(f"Creating candidate for project {candidate.proj_id}")
        if not candidate.candidate_info:
            logger.error("Candidate info is required")
            raise HTTPException(status_code=400, detail="Candidate info is required")

        project = get_project_by_id(candidate.proj_id)
        if project is None:
            logger.warning(f"Project {candidate.proj_id} not found")
            raise HTTPException(status_code=404, detail="Project not found")

        personal_info = candidate.candidate_info.get("personal_info")
        if personal_info is None:
            raise HTTPException(status_code=404, detail="personal_info not found in json")

        email_field = personal_info.get("email")
        if email_field is None:
            raise HTTPException(status_code=404, detail="email not found in personal_info")

        valid_emails, error_msg = verify_emails(email_field)

        for email in valid_emails:
            email = email.strip()
            if email:
                existing_candidate = get_candidate_by_email(email)
                if existing_candidate is not None:
                    raise HTTPException(status_code=400, detail=f"Candidate with email {email} already exists")

        if not valid_emails:
            raise HTTPException(status_code=400, detail=f"Invalid email address: {error_msg}")

        if candidate.candidate_info.get("work_experience") is None:
            raise HTTPException(status_code=404, detail="work_experience not found in json")

        new_candidate = create_candidate(candidate)
        if new_candidate is None:
            logger.error("Error creating candidate")
            raise HTTPException(status_code=500, detail="Error Creating Candidate")

        logger.info(f"Candidate {new_candidate.id} created successfully")
        return new_candidate

    except psycopg2.Error as e:
        logger.error(f"Database error create_candidate_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error create_candidate_endpoint: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException create_candidate_endpoint: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error creating candidate: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Creating Candidate: {str(e)}")


# Update candidate
# This endpoint updates an existing candidate's information
@router.put("/", response_model=Candidate)
def update_candidate_endpoint(candidate: CandidateUpdate):
    """
    Update an existing candidate's information.

    Args:
        candidate (CandidateUpdate): The candidate data to update.

    Returns:
        Candidate: The updated candidate object.

    Raises:
        HTTPException: If candidate or project is not found, or update fails.
    """
    try:
        if not candidate.candidate_info:
            raise HTTPException(status_code=400, detail="Candidate info is required")

        current_candidate = get_candidate_by_id(candidate.id)
        if current_candidate is None:
            raise HTTPException(status_code=404, detail="Candidate not found")

        project = get_project_by_id(candidate.proj_id)
        if project is None:
            raise HTTPException(status_code=404, detail="Project not found")

        updated_candidate = update_candidate(candidate)
        if updated_candidate is None:
            raise HTTPException(status_code=500, detail="Error Updating Candidate")

        return updated_candidate
    except psycopg2.Error as e:
        logger.error(f"Database error update_candidate_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error update_candidate_endpoint: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException update_candidate_endpoint: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error updating candidate: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Updating Candidate: {str(e)}")


# Get candidate by ID
# This endpoint retrieves a candidate by their ID
@router.get("/{candidate_id}", response_model=Candidate)
def get_candidate_by_id_endpoint(candidate_id: str):
    """
    Retrieve a candidate by their unique ID.

    Args:
        candidate_id (str): The candidate's unique identifier.

    Returns:
        Candidate: The candidate object.

    Raises:
        HTTPException: If candidate is not found.
    """
    try:
        candidate = get_candidate_by_id(candidate_id)
        if candidate is None:
            raise HTTPException(status_code=404, detail="Candidate not found")
        return candidate
    except psycopg2.Error as e:
        logger.error(f"Database error get_candidate_by_id_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error get_candidate_by_id_endpoint: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException get_candidate_by_id_endpoint: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error retrieving candidate: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Retrieving Candidate: {str(e)}")


# Get candidates with pagination
# This endpoint retrieves candidates with pagination based on the provided page number and chunk size
@router.post("/candidates_pagination/", response_model=PaginationResponse)
def get_candidates_pagination_endpoint(page: int, chunk_size: int = 10, filters: CandidateFilters | None = None):
    """
    Retrieve candidates with pagination.

    Args:
        page (int): The page number.
        chunk_size (int, optional): Number of candidates per page. Defaults to 10.
        filters (CandidateFilters, optional): Filters to apply.

    Returns:
        PaginationResponse: Paginated list of candidates.
    """
    try:
        candidates = get_candidates_page(page=page, chunk_size=chunk_size, filters=filters)
        total_candidates = get_total_candidates(filters=filters)

        if not candidates:
            return PaginationResponse(
                total_items=0,
                items=[]
            )

        return PaginationResponse(
            total_items=total_candidates,
            items=candidates
        )
    except psycopg2.Error as e:
        logger.error(f"Database error get_candidates_pagination_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error get_candidates_pagination_endpoint: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException get_candidates_pagination_endpoint: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error fetching candidates: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Fetching Candidates: {str(e)}")


# Export candidate
# This endpoint exports a candidate's information in the specified document type (PDF or DOCX)
@router.get("/{candidate_id}/export")
def get_candidate_endpoint(candidate_id: str, document_type: str):
    """
    Export a candidate's information in the specified document type (PDF or DOCX).

    Args:
        candidate_id (str): The candidate's unique identifier.
        document_type (str): The document type ("pdf" or "docx").

    Returns:
        StreamingResponse: The exported file as a stream.

    Raises:
        HTTPException: If candidate is not found or document type is unsupported.
    """
    try:
        logger.info("trying to export candidate with the id " + candidate_id + "in " + document_type)
        cand = get_candidate_by_id(candidate_id)

        if cand is None:
            raise HTTPException(status_code=404, detail="Candidate not found")

        logger.info("candidate info retrieve successfully")
        file_extension = document_type.lower()
        file_content = ""

        if file_extension == "pdf":
            media_type = "application/pdf"
            file_content = create_candidate_pdf(cand)
        elif file_extension == "docx":
            media_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            file_content = create_candidate_docx(cand)
        else:
            raise HTTPException(status_code=400, detail="Unsupported document type")

        logger.info("retrive file with the length" + str(len(file_content)))

        # Return the generated document as a stream
        file_name = get_filename(cand, file_extension)  # f"candidate_{candidate_id}.{file_extension}"
        file_stream = io.BytesIO(file_content)

        return StreamingResponse(
            iter([file_stream.getvalue()]),
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={file_name}"}
        )
    except psycopg2.Error as e:
        logger.error(f"Database error get_candidate_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error get_candidate_endpoint: {str(e)}")
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error occurred while exporting candidate: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error occurred while exporting candidate: {str(e)}")


# Enable candidate
# This endpoint enables a candidate based on the provided candidate_id
@router.post("/{candidate_id}/enable")
def enable_candidate_endpoint(candidate_id: str):
    """
    Enable a candidate based on the provided candidate_id.

    Args:
        candidate_id (str): The candidate's unique identifier.

    Returns:
        Candidate: The enabled candidate object.

    Raises:
        HTTPException: If candidate is not found or enabling fails.
    """
    try:    
        logger.info("trying to enable candidate with the id " + candidate_id)
        cand = get_candidate_by_id(candidate_id)
        if cand is None:
            raise HTTPException(status_code=404, detail="Candidate not found")

        logger.info("candidate info retrieve successfully")

        candidate = change_candidate_status(candidate_id, True, cand.reason_info)

        return candidate
    except psycopg2.Error as e:
        logger.error(f"Database error get_candidate_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error get_candidate_endpoint: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException get_candidate_endpoint: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error enabling candidate: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Enabling Candidate: {str(e)}")


# Disable candidate
# This endpoint disables a candidate based on the provided candidate_id
@router.post("/{candidate_id}/disable")
def disable_candidate_endpoint(candidate_id: str, reason_info: dict):
    """
    Disable a candidate based on the provided candidate_id and reason.

    Args:
        candidate_id (str): The candidate's unique identifier.
        reason_info (dict): The reason for disabling.

    Returns:
        Candidate: The disabled candidate object.

    Raises:
        HTTPException: If candidate is not found or disabling fails.
    """
    try:
        logger.info("trying to disable candidate with the id " + candidate_id)
        cand = get_candidate_by_id(candidate_id)
        if cand is None:
            raise HTTPException(status_code=404, detail="Candidate not found")

        logger.info("candidate info retrieve successfully")

        candidate = change_candidate_status(candidate_id, False, reason_info)

        return candidate
    except psycopg2.Error as e:
        logger.error(f"Database error get_candidate_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error get_candidate_endpoint: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException get_candidate_endpoint: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error disabling candidate: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Disabling Candidate: {str(e)}")


# Validate candidate
# This endpoint validates a candidate based on Levenshtein distance
@router.post("/validate_candidate", response_model=ValidateCandidateResponse, summary="Validate a candidate based on Levenshtein distance")
async def validate_candidate_endpoint(request: ValidateCandidateRequest):
    """
    Validate a candidate based on Levenshtein distance.

    Args:
        request (ValidateCandidateRequest): The validation request data.

    Returns:
        ValidateCandidateResponse: The validation result.
    """
    try:
        result = validate_candidate(request)
        return result
    except psycopg2.Error as e:
        logger.error(f"Database error validate_candidate_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error validate_candidate_endpoint: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException validate_candidate_endpoint: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error validating candidate: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Validating Candidate: {str(e)}")


# Get all candidates
# This endpoint retrieves all candidates from the database
@router.get("/", response_model=List[Candidate])
def get_all_candidates_endpoint():
    """
    Retrieve all candidates from the database.

    Returns:
        List[Candidate]: List of all candidates.
    """
    try:
        candidates = fetch_all_candidates()
        if not candidates:
            return []
        return candidates
    except psycopg2.Error as e:
        logger.error(f"Database error get_all_candidates_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error get_all_candidates_endpoint: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException get_all_candidates_endpoint: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error retrieving all candidates: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Retrieving All Candidates: {str(e)}")


# Delete candidate, this is a soft delete
# This endpoint deletes a candidate based on the provided candidate_id
@router.delete("/{candidate_id}/delete")
def candidate_delete(candidate_id: str, reason_info: dict):
    """
    Soft delete a candidate based on the provided candidate_id.

    Args:
        candidate_id (str): The candidate's unique identifier.
        reason_info (dict): The reason for deletion.

    Returns:
        Any: The deletion response.

    Raises:
        HTTPException: If candidate is not found or deletion fails.
    """
    try:
        logger.info(f"Deleting candidate with ID: {candidate_id}, Reason: {reason_info}")
        # Validate candidate_id
        if not candidate_id:
            raise HTTPException(status_code=400, detail="Candidate ID is required")
        if not reason_info:
            raise HTTPException(status_code=400, detail="Reason info is required")
        # Check if candidate exists
        candidate = get_candidate_by_id(candidate_id)
        if candidate is None:
            raise HTTPException(status_code=404, detail="Candidate not found")
        # Delete candidate by candidate_id
        response = delete_candidate(candidate_id, reason_info)
        return response
    except psycopg2.Error as e:
        logger.error(f"Database error candidate_delete: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error candidate_delete: {str(e)}")
    except HTTPException as e:
        logger.error(f"Candidate deletion failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Error deleting candidate: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Deleting Candidate: {str(e)}")


# Upload files
# This endpoint allows uploading multiple files and processes them using the Lumus API
@router.post("/uploadfiles/")
async def create_upload_files(files: List[UploadFile] = File(...), project_id: str = "", created_by: str = "") -> List[UploadFileResponse]:
    """
    Upload multiple files and process them using the Lumus API.

    Args:
        files (List[UploadFile]): List of files to upload.
        project_id (str): The project ID.
        created_by (str): The user who created the upload.

    Returns:
        List[UploadFileResponse]: List of processed file responses.

    Raises:
        HTTPException: If required fields are missing or upload fails.
    """
    try:
        if not files:
            raise HTTPException(status_code=400, detail="No files provided")
        if not project_id:
            raise HTTPException(status_code=400, detail="Project ID is required")
        if not created_by:
            raise HTTPException(status_code=400, detail="Created by is required")

        # Process files concurrently
        file_responses = await extract_data_lumus(files)

        # Validate candidate batch
        validated_files = validate_candidate_batch(file_responses, project_id, created_by)

        return validated_files
    except psycopg2.Error as e:
        logger.error(f"Database error create_upload_files: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error create_upload_files: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException create_upload_files: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error uploading files: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Uploading Files: {str(e)}")


# Validate candidate by email
# This endpoint validates a candidate by their email address
@router.get("/{email}/email")
def validate_candidate_by_email(email: str):
    """
    Validate a candidate by their email address.

    Args:
        email (str): The candidate's email address.

    Returns:
        Any: The candidate object if found.

    Raises:
        HTTPException: If email is invalid or candidate is not found.
    """
    try:
        # Validate email format
        if not is_valid_email(email):
            raise HTTPException(status_code=400, detail="Invalid email format")
        # Validate candidate by email
        response = get_candidate_by_email(email)
        if response is None:
            raise HTTPException(status_code=404, detail="Candidate not found")
        return response
    except psycopg2.Error as e:
        logger.error(f"Database error validate_candidate_by_email: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error validate_candidate_by_email: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException validate_candidate_by_email: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error validating candidate by email: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Validating Candidate by Email: {str(e)}")


# Validate candidate batch
# This function validates a batch of candidates by checking if they already exist in the database
def validate_candidate_batch(file_responses: List[UploadFileResponse], project_id: str, created_by: str):
    """
    Validate a batch of candidates by checking if they already exist in the database.

    Args:
        file_responses (List[UploadFileResponse]): List of file responses to validate.
        project_id (str): The project ID.
        created_by (str): The user who created the upload.

    Returns:
        List[UploadFileResponse]: List of validated file responses.
    """
    for file in file_responses:
        if file.error is False and file.error_message == "" and file.candidate_info != {}:
            try:
                emails = file.candidate_info.get("personal_info", {}).get("email", "")
                valid_emails, error_msg = verify_emails(emails)
                if error_msg:
                    file.error = True
                    file.error_message = error_msg
                    logger.error(f"Validation failed for candidate info: {file.candidate_info}. Error: {error_msg}")
                    break

                valid = None
                valid_an_email = False
                for email in valid_emails:
                    email = email.strip()
                    if email:
                        valid = get_candidate_by_email(email)
                        if valid is not None:
                            file.candidate_id = valid.id
                            file.error = True
                            file.error_message = "duplicated"
                            logger.error(f"Validation failed for candidate info: {file.candidate_info}. Error: duplicated")
                            break
                        else:
                            valid_an_email = True
                if file.error is True:
                    # If candidate already exists, set error
                    logger.error(f"Candidate with email {email} already exists: {file.candidate_info}")
                    continue
                # If no valid email found, set error
                if valid_an_email:
                    file.candidate_info["personal_info"]["email"] = ';'.join(valid_emails)
                else:
                    file.error = True
                    file.error_message = "No valid email found"
                    logger.error(f"Validation failed for candidate info: {file.candidate_info}. Error: No valid email found")
                    break

                if file.error is not True and valid_an_email is True:
                    logger.info(f"Creating candidate for project {project_id} with info: {file.candidate_info}")
                    candidate = create_candidate(
                        CandidateCreate(
                            id="",
                            candidate_info=file.candidate_info,
                            proj_id=project_id,
                            suggested_positions=[],
                            analysis_status="string",
                            created_by=created_by
                        )
                    )
                    file.candidate_id = candidate.id
                    file.candidate_info = candidate.candidate_info
                    file.error = False
                    file.error_message = "created"
                else:
                    logger.error(f"Candidate creation failed for project {project_id} with info: {file.candidate_info}. Error: {file.error_message}")
                    # If validation failed or candidate already exists, set error
                    file.error = True
                    file.error_message = "Validation failed or candidate already exists"
            except Exception as e:
                file.error = True
                file.error_message = str(e)
                logger.error(f"Validation failed for candidate info: {file.candidate_info}. Error: {e}")
    return file_responses


# Get job titles
# This function retrieves distinct job titles from candidates' work experience
@router.get("/get/roles/", response_model=List[str], summary="Get distinct job titles from candidates' work experience")
def get_job_titles_endpoint() -> List[str]:
    """
    Retrieve distinct job titles from candidates' work experience.

    Returns:
        List[str]: List of job titles.
    """
    try:
        return get_job_titles()
    except psycopg2.Error as e:
        logger.error(f"Database error get_job_titles_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error get_job_titles_endpoint: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException get_job_titles_endpoint: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error retrieving job titles: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Retrieving Job Titles: {str(e)}")


# Get countries
# This function retrieves distinct countries from candidates' work experience
@router.get("/get/countries/", response_model=List[str], summary="Get distinct countries from candidates' work experience")
def get_countries_endpoint() -> List[str]:
    """
    Retrieve distinct countries from candidates' work experience.

    Returns:
        List[str]: List of countries.
    """
    # token = get_token()  # Call to get token, if needed
    # verify_token(token, os.environ.get("CLIENT_ID_SMART_HR"))  # Validate the token
    try:
        return get_countries()
    except psycopg2.Error as e:
        logger.error(f"Database error get_countries_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error get_countries_endpoint: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException get_countries_endpoint: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error retrieving countries: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Retrieving Countries: {str(e)}")


# Update all candidates to ensure their embeddings are up-to-date
# This is a placeholder for the actual update logic, which might involve re-embedding candidates
@router.post("/update_all_candidates/")
def update_all_candidates_endpoint():
    """
    Update all candidates to ensure their embeddings are up-to-date.

    Returns:
        dict: Message and list of updated candidates.
    """
    try:
        # Call the function to update all candidates
        candidates = update_all_candidates()
        return {"message": "All candidates updated successfully", "candidates": candidates}
    except psycopg2.Error as e:
        logger.error(f"Database error update_all_candidates_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error update_all_candidates_endpoint: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException update_all_candidates_endpoint: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error updating all candidates: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Updating All Candidates: {str(e)}")


# Verify JWT token
# This endpoint verifies the validity of a JWT token
@router.get("/verify_token/")
def verify_token_endpoint(token: str):
    """
    Verify the validity of a JWT token.

    Args:
        token (str): The JWT token to verify.

    Returns:
        dict: Message and claims if valid.

    Raises:
        HTTPException: If token is invalid.
    """
    is_valid = validate_token(token)
    if is_valid:
        return {"message": "Token is valid", "claims": is_valid}
    else:
        raise HTTPException(status_code=401, detail="Invalid token")


# Retrieve matching positions for a candidate
@router.get("/candidate_match_positions/")
def candidate_match_positions_endpoint(candidate_id: str):
    """
    Retrieve matching positions for a candidate.

    Args:
        candidate_id (str): The candidate's unique identifier.

    Returns:
        dict: Matching positions or message if none found.
    """
    try:
        if not candidate_id:
            raise HTTPException(status_code=400, detail="Candidate ID is required")
        positions = candidate_match_positions(candidate_id)
        if not positions:
            return {"message": "No matching positions found for the candidate"}
        return {"positions": positions}
    except psycopg2.Error as e:
        logger.error(f"Database error candidate_match_positions_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error candidate_match_positions_endpoint: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException candidate_match_positions_endpoint: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error retrieving candidate match positions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Retrieving Candidate Match Positions: {str(e)}")
